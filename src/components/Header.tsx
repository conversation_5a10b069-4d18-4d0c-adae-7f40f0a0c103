import { Search, User, Globe, Bed, Tag, HelpCircle, Crown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

const Header = () => {
  return (
    <header className="bg-nav-bg text-primary-foreground">
      {/* Top bar */}
      <div className=".border-b bg-black">
        <div className="container mx-auto px-4 flex items-center justify-between h-12 text-sm">
          <div className="flex items-center gap-1">
            <Globe size={16} />
            <span>US</span>
          </div>
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-1">
              <Bed size={16} />
              <span>Hotels</span>
            </div>
            <span>Sell</span>
            <div className="flex items-center gap-1">
              <Tag size={16} />
              <span>Gift Cards</span>
            </div>
            <span>Help</span>
            <div className="flex items-center gap-1">
              <Crown size={16} />
              <span>VIP</span>
            </div>
            <div className="flex items-center gap-1">
              <span>PayPal</span>
              <span className="text-xs">THE PREFERRED PAYMENTS PARTNER</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main header */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-8">
            <div className="text-2xl font-bold">
              ticketmaster®
            </div>
            <nav className="hidden lg:flex items-center gap-6">
              <span className="hover:text-gray-200 cursor-pointer">Concerts</span>
              <span className="hover:text-gray-200 cursor-pointer">Sports</span>
              <span className="hover:text-gray-200 cursor-pointer">Arts, Theater & Comedy</span>
              <span className="hover:text-gray-200 cursor-pointer">Family</span>
              <span className="hover:text-gray-200 cursor-pointer">Cities</span>
            </nav>
          </div>
          
          <div className="flex items-center gap-4">
            <div className="flex items-center bg-white/10 border rounded p-2">
              <Input 
                placeholder="Artist, Event or Venue" 
                className="border-0 bg-transparent text-gray-900 placeholder:text-white w-80 hidden md:block"
              />
              <Button variant="ghost" size="sm" className="text-white hover:text-gray-700">
                <Search size={20} />
              </Button>
            </div>
            
            <Button variant="ghost" className="flex items-center gap-2 text-primary-foreground hover:text-gray-200">
              <User size={20} />
              <span className="hidden md:block">My Account</span>
            </Button>
          </div>
        </div>

        {/* Mobile search */}
        <div className="md:hidden mt-4">
          <div className="flex items-center bg-white rounded">
            <Input 
              placeholder="Artist, Event or Venue" 
              className="border-0 bg-transparent text-gray-900 placeholder:text-gray-500"
            />
            <Button variant="ghost" size="sm" className="text-gray-500 hover:text-gray-700">
              <Search size={20} />
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;