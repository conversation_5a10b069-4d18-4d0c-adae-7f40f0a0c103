import { ChevronUp, ChevronDown, Ticket, User, Settings, LogOut, HelpCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { useState } from "react";

const Sidebar = () => {
  const [isTicketsOpen, setIsTicketsOpen] = useState(true);
  const [isProfileOpen, setIsProfileOpen] = useState(false);

  return (
    <aside className="w-80 bg-sidebar-bg border-r border-border h-full">
      <div className="p-4">

        <div className=".bg-nav-bg text-primary-foreground pb-8">
          <div>
            <div className="flex flex-col items-start gap-8">
              {/* Large 't' logo */}
              <div className="bg-primary-hover rounded-lg p-8 text-6xl font-bold text-center min-w-[120px]">
                t
              </div>

              <div className="text-black">
                <div className="text-sm mb-1 font-semibold">Welcome back!</div>
                <div className="text-base">Oluwafemi</div>
              </div>
            </div>
          </div>
        </div>

        {/* My Tickets Section */}
        <Collapsible open={isTicketsOpen} onOpenChange={setIsTicketsOpen}>
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              className="w-full justify-between p-3 h-auto font-normal text-left hover:bg-accent"
            >
              <div className="flex items-center gap-2">
                <Ticket size={20} />
                <span>My Tickets</span>
              </div>
              {isTicketsOpen ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-1 pl-4 mt-2">
            <Button variant="ghost" className="w-full justify-start py-2 px-4 text-sm font-normal">
              Upcoming Events
            </Button>
            <Button variant="ghost" className="w-full justify-start py-2 px-4 text-sm font-normal">
              Past Events
            </Button>
            <Button variant="ghost" className="w-full justify-start py-2 px-4 text-sm font-normal">
              My Listings
            </Button>
            <Button variant="ghost" className="w-full justify-start py-2 px-4 text-sm font-normal">
              My Digital Collectibles
            </Button>
          </CollapsibleContent>
        </Collapsible>

        {/* My Profile Section */}
        <Collapsible open={isProfileOpen} onOpenChange={setIsProfileOpen} className="mt-4">
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              className="w-full justify-between p-3 h-auto font-normal text-left hover:bg-accent"
            >
              <div className="flex items-center gap-2">
                <User size={20} />
                <span>My Profile</span>
              </div>
              {isProfileOpen ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-1 pl-4 mt-2">
            <Button variant="ghost" className="w-full justify-start py-2 px-4 text-sm font-normal">
              Account Details
            </Button>
            <Button variant="ghost" className="w-full justify-start py-2 px-4 text-sm font-normal">
              Preferences
            </Button>
          </CollapsibleContent>
        </Collapsible>

        {/* My Settings Section */}
        <div className="mt-4">
          <Button
            variant="ghost"
            className="w-full justify-between p-3 h-auto font-normal text-left hover:bg-accent"
          >
            <div className="flex items-center gap-2">
              <Settings size={20} />
              <span>My Settings</span>
            </div>
            <ChevronDown size={20} />
          </Button>
        </div>

        {/* Sign Out */}
        <div className="mt-4">
          <Button
            variant="ghost"
            className="w-full justify-start p-3 h-auto font-normal text-left hover:bg-accent"
          >
            <div className="flex items-center gap-2">
              <LogOut size={20} />
              <span>Sign Out</span>
            </div>
          </Button>
        </div>

        {/* Need Help */}
        <div className="mt-8">
          <Button
            variant="ghost"
            className="w-full justify-start p-3 h-auto font-normal text-left hover:bg-accent"
          >
            <div className="flex items-center gap-2">
              <HelpCircle size={20} />
              <span>Need Help?</span>
            </div>
          </Button>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;