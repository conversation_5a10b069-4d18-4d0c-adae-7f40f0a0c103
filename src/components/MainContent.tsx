import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Ticket } from "lucide-react";

const MainContent = () => {
  return (
    <div className="flex-1 bg-background">
      {/* Blue header section with breadcrumb and title */}
      <div className="bg-nav-bg text-primary-foreground">
        {/* Breadcrumb */}
        <div className="px-6 py-3">
          <div className="text-sm">
            Home / My Tickets
          </div>
        </div>

        {/* My Tickets title and tabs */}
        <div className="px-6 pb-8">
          <h1 className="text-4xl font-bold mb-6">My Tickets</h1>

          <Tabs defaultValue="upcoming" className="w-full">
            <TabsList className="bg-transparent border-0 p-0 h-auto gap-8">
              <TabsTrigger
                value="upcoming"
                className="bg-transparent border-0 border-b-2 border-transparent data-[state=active]:border-white data-[state=active]:bg-transparent text-white/70 data-[state=active]:text-white rounded-none px-0 pb-2"
              >
                Upcoming Events
              </TabsTrigger>
              <TabsTrigger
                value="past"
                className="bg-transparent border-0 border-b-2 border-transparent data-[state=active]:border-white data-[state=active]:bg-transparent text-white/70 data-[state=active]:text-white rounded-none px-0 pb-2"
              >
                Past Events
              </TabsTrigger>
              <TabsTrigger
                value="listings"
                className="bg-transparent border-0 border-b-2 border-transparent data-[state=active]:border-white data-[state=active]:bg-transparent text-white/70 data-[state=active]:text-white rounded-none px-0 pb-2"
              >
                My Listings
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      {/* Main content area */}
      <div className="px-6 py-8 bg-gray-50 min-h-screen">
        <Tabs defaultValue="upcoming" className="w-full">
          <TabsContent value="upcoming" className="mt-0">
            <div className="flex flex-col items-center justify-center py-16 text-center">
              <div className="bg-gray-200 rounded-full p-8 mb-6">
                <Ticket size={48} className="text-gray-400" />
              </div>

              <h2 className="text-2xl font-bold mb-4 text-gray-800">No upcoming events</h2>
              <p className="text-gray-600 mb-8 max-w-md">
                Tickets you buy will automatically appear here. Browse events to find tickets to something awesome.
              </p>

              <Button className="bg-primary hover:bg-primary-hover text-primary-foreground px-8 py-3 text-lg rounded-md">
                Browse Events
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="past" className="mt-0">
            <div className="flex flex-col items-center justify-center py-16 text-center">
              <div className="bg-gray-200 rounded-full p-8 mb-6">
                <Ticket size={48} className="text-gray-400" />
              </div>

              <h2 className="text-2xl font-bold mb-4 text-gray-800">No past events</h2>
              <p className="text-gray-600 mb-8 max-w-md">
                Your past event tickets will appear here.
              </p>
            </div>
          </TabsContent>

          <TabsContent value="listings" className="mt-0">
            <div className="flex flex-col items-center justify-center py-16 text-center">
              <div className="bg-gray-200 rounded-full p-8 mb-6">
                <Ticket size={48} className="text-gray-400" />
              </div>

              <h2 className="text-2xl font-bold mb-4 text-gray-800">No active listings</h2>
              <p className="text-gray-600 mb-8 max-w-md">
                Tickets you sell will appear here.
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default MainContent;