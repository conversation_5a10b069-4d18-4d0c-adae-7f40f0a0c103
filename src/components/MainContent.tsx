import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs";
import { Ticket } from "lucide-react";

const MainContent = () => {
  return (
    <div className="flex-1 bg-background">
      {/* Breadcrumb */}
      <div className="bg-nav-bg text-primary-foreground py-3">
        <div className="container mx-auto px-6">
          <div className="text-sm">
            Home / My Tickets
          </div>
        </div>
      </div>

      {/* Main content area */}
      <div className="container mx-auto px-6 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold mb-6">My Tickets</h1>
          
          <Tabs defaultValue="upcoming" className="w-full">
            <TabsList className="grid w-full grid-cols-3 max-w-md">
              <TabsTrigger value="upcoming">Upcoming Events</TabsTrigger>
              <TabsTrigger value="past">Past Events</TabsTrigger>
              <TabsTrigger value="listings">My Listings</TabsTrigger>
            </TabsList>
            
            <TabsContent value="upcoming" className="mt-8">
              <div className="flex flex-col items-center justify-center py-16 text-center">
                <div className="bg-gray-100 rounded-full p-8 mb-6">
                  <Ticket size={48} className="text-gray-400" />
                </div>
                
                <h2 className="text-2xl font-bold mb-4">No upcoming events</h2>
                <p className="text-muted-foreground mb-8 max-w-md">
                  Tickets you buy will automatically appear here. Browse events to find tickets to something awesome.
                </p>
                
                <Button className="bg-primary hover:bg-primary-hover text-primary-foreground px-8 py-3 text-lg">
                  Browse Events
                </Button>
              </div>
            </TabsContent>
            
            <TabsContent value="past" className="mt-8">
              <div className="flex flex-col items-center justify-center py-16 text-center">
                <div className="bg-gray-100 rounded-full p-8 mb-6">
                  <Ticket size={48} className="text-gray-400" />
                </div>
                
                <h2 className="text-2xl font-bold mb-4">No past events</h2>
                <p className="text-muted-foreground mb-8 max-w-md">
                  Your past event tickets will appear here.
                </p>
              </div>
            </TabsContent>
            
            <TabsContent value="listings" className="mt-8">
              <div className="flex flex-col items-center justify-center py-16 text-center">
                <div className="bg-gray-100 rounded-full p-8 mb-6">
                  <Ticket size={48} className="text-gray-400" />
                </div>
                
                <h2 className="text-2xl font-bold mb-4">No active listings</h2>
                <p className="text-muted-foreground mb-8 max-w-md">
                  Tickets you sell will appear here.
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default MainContent;