import { Facebook, Twitter, Youtube, Instagram } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const Footer = () => {
  return (
    <footer className="bg-footer-bg text-footer-text">
      <div className="container mx-auto px-6 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand and Social */}
          <div className="lg:col-span-1">
            <div className="text-white text-2xl font-bold mb-6">
              ticketmaster®
            </div>
            
            <div className="mb-6">
              <h3 className="text-white font-medium mb-4">Let's connect</h3>
              <div className="flex gap-3">
                <Button variant="ghost" size="sm" className="p-2 text-footer-text hover:text-white">
                  <Facebook size={20} />
                </Button>
                <Button variant="ghost" size="sm" className="p-2 text-footer-text hover:text-white">
                  <Twitter size={20} />
                </Button>
                <Button variant="ghost" size="sm" className="p-2 text-footer-text hover:text-white bg-blue-600">
                  BLOG
                </Button>
                <Button variant="ghost" size="sm" className="p-2 text-footer-text hover:text-white">
                  <Youtube size={20} />
                </Button>
                <Button variant="ghost" size="sm" className="p-2 text-footer-text hover:text-white">
                  <Instagram size={20} />
                </Button>
              </div>
            </div>

            <div>
              <h3 className="text-white font-medium mb-4">Download Our Apps</h3>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" className="text-xs bg-black border-gray-600 text-white hover:bg-gray-800">
                  Download on the App Store
                </Button>
                <Button variant="outline" size="sm" className="text-xs bg-black border-gray-600 text-white hover:bg-gray-800">
                  GET IT ON Google Play
                </Button>
              </div>
            </div>
          </div>

          {/* Helpful Links */}
          <div>
            <h3 className="text-white font-medium mb-4">Helpful Links</h3>
            <ul className="space-y-2 text-sm">
              <li><a href="#" className="hover:text-white">Help/FAQ</a></li>
              <li><a href="#" className="hover:text-white">Sell</a></li>
              <li><a href="#" className="hover:text-white">My Account</a></li>
              <li><a href="#" className="hover:text-white">Contact Us</a></li>
              <li><a href="#" className="hover:text-white">Gift Cards</a></li>
              <li><a href="#" className="hover:text-white">Refunds and Exchanges</a></li>
              <li><a href="#" className="hover:text-white">Do Not Sell or Share My Personal Information</a></li>
            </ul>
          </div>

          {/* Our Network */}
          <div>
            <h3 className="text-white font-medium mb-4">Our Network</h3>
            <ul className="space-y-2 text-sm">
              <li><a href="#" className="hover:text-white">Live Nation</a></li>
              <li><a href="#" className="hover:text-white">House of Blues</a></li>
              <li><a href="#" className="hover:text-white">Front Gate Tickets</a></li>
              <li><a href="#" className="hover:text-white">TicketWeb</a></li>
              <li><a href="#" className="hover:text-white">universe</a></li>
              <li><a href="#" className="hover:text-white">NFL</a></li>
              <li><a href="#" className="hover:text-white">NBA</a></li>
              <li><a href="#" className="hover:text-white">NHL</a></li>
            </ul>
          </div>

          {/* About Us */}
          <div>
            <h3 className="text-white font-medium mb-4">About Us</h3>
            <ul className="space-y-2 text-sm">
              <li><a href="#" className="hover:text-white">Ticketmaster Blog</a></li>
              <li><a href="#" className="hover:text-white">Ticketing Truths</a></li>
              <li><a href="#" className="hover:text-white">Privacy Policy</a></li>
              <li><a href="#" className="hover:text-white">Ad Choices</a></li>
              <li><a href="#" className="hover:text-white">Careers</a></li>
              <li><a href="#" className="hover:text-white">Ticket Your Event</a></li>
              <li><a href="#" className="hover:text-white">Innovation</a></li>
            </ul>
          </div>

          {/* Friends & Partners */}
          <div>
            <h3 className="text-white font-medium mb-4">Friends & Partners</h3>
            <ul className="space-y-2 text-sm">
              <li><a href="#" className="hover:text-white">PayPal</a></li>
              <li><a href="#" className="hover:text-white">Allianz</a></li>
              <li><a href="#" className="hover:text-white">AWS</a></li>
            </ul>
          </div>
        </div>

        {/* Bottom text */}
        <div className="mt-8 pt-8 border-t border-gray-700">
          <p className="text-sm">
            By continuing past this page, you agree to our{" "}
            <a href="#" className="text-white hover:underline">terms of use</a>
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;